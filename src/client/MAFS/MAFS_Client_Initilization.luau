--!strict

--[[
    Footstep_initialization.client.luau

    Client-side script for initializing the footstep system

    ARCHITECTURE ROLE:
    - Entry point for the client footstep system
    - Loads and initializes the FootstepClient module
    - Demonstrates the public API usage

    PLACEMENT:
    - This script should be placed in a client-side location (StarterPlayerScripts)

    *Dynamic Innovative Studio*
]]

-- Services

-- Reference the client-side FootstepClient module
local FootstepClient = script.Parent:FindFirstChild("FootStep_Client")
if not FootstepClient then
  warn("MAFS: FootstepClient module not found in expected location")
  return
end

-- Initialize the client-side footstep system
local footstepSystem = require(script.Parent.MAFS_Client_Module)
local success = footstepSystem.Initialize()

if success then
  print("MAFS: Client-side footstep system initialized successfully")
else
  warn("MAFS: Failed to initialize client-side footstep system")
end

--[[
    PUBLIC API EXAMPLES

    The footstep system provides the following public APIs that can be used
    by other client scripts to interact with the system:

    -- Set the volume of footsteps (0-1)
    footstepSystem.SetVolume(0.8)

    -- Disable footsteps temporarily (useful for cutscenes)
    footstepSystem.SetEnabled(false)

    -- Re-enable footsteps after disabling
    footstepSystem.SetEnabled(true)
]]
