--!strict

--[[
    MAFS_Configuration.luau
    
    Modular Audio FootStep System - Core Configuration Module
    
    Version: 1.1.0
    Author: BleckWolf25
    Contributors: Dynamic Innovative Studio Team
    Copyright: Dynamic Innovative Studio
    
    DESCRIPTION:
    Central configuration module for the MAFS (Modular Audio FootStep) system.
    Provides centralized material sound definitions, system settings, and utility functions.
    
    USAGE:
    local MAFSConfig = require(ReplicatedStorage.Configurations.Systems.MAFS_Configuration)
    local grassData = MAFSConfig.GetMaterialData(Enum.Material.Grass)
    local soundId = MAFSConfig.GetRandomSoundId(grassData)
    
    PERFORMANCE:
    - O(1) material lookups via hash tables
    - Optimized random sound selection
    - Cached configuration for session
    
    SECURITY:
    - Runtime validation of all configuration values
    - Rate limiting to prevent abuse
    - Anti-teleport protection via distance validation
]]

-- ============================================================================
-- TYPE ANNOTATIONS & INFER USAGE
-- ============================================================================

-- Playback speed range tuple (min, max)
type PlaybackSpeedRange = { number }

-- Material data structure
type MaterialData = {
  SoundIds: { string },
  Volume: number,
  PlaybackSpeedRange: PlaybackSpeedRange,
  RollOffMinDistance: number,
  RollOffMaxDistance: number,
}

-- MAFS Settings structure
type MAFSSettings = {
  DebugMode: boolean,
  EnablePerformanceMetrics: boolean,
  MaxCachedSounds: number,
  BroadcastRadius: number,
  ServerCooldown: number,
  ClientCooldown: number,
  MovementThreshold: number,
  StepInterval: number,
  MaxDistanceDelta: number,
}

-- MAFS Configuration structure
type MAFSConfigType = {
  Settings: MAFSSettings,
  MaterialSounds: { [Enum.Material]: MaterialData },
  CustomMaterials: { [string]: MaterialData },
  DefaultMaterial: MaterialData,

  GetMaterialData: (material: Enum.Material | string) -> MaterialData,
  GetRandomSoundId: (materialData: MaterialData) -> string,
  GetRandomPlaybackSpeed: (materialData: MaterialData) -> number,
  IsDebugMode: () -> boolean,
  SetDebugMode: (enabled: boolean) -> (),
  ValidateConfiguration: () -> boolean,
  GetAvailableMaterials: () -> { string },
}

-- ============================================================================
-- DEFINE LUAU MODULE
-- ============================================================================
local MAFSConfig = {} :: MAFSConfigType

-- ============================================================================
-- MAFS SYSTEM SETTINGS
-- ============================================================================

-- Core system configuration settings
-- Performance Impact: Higher BroadcastRadius increases network traffic
-- Security: Cooldowns prevent spam, MaxDistanceDelta prevents teleport exploits
-- Audio Quality: Lower StepInterval creates more realistic footsteps
MAFSConfig.Settings = {
  DebugMode = false,                    -- Enable debug logging and diagnostics
  EnablePerformanceMetrics = true,      -- Track system performance metrics
  MaxCachedSounds = 20,                 -- Maximum sounds in object pool
  BroadcastRadius = 50,                 -- Maximum distance for footstep broadcasting (studs)
  ServerCooldown = 0.25,                -- Minimum time between server validations (seconds)
  ClientCooldown = 0.27,                -- Minimum time between client requests
  MovementThreshold = 0.1,              -- Minimum movement distance to trigger footstep (studs)
  StepInterval = 0.3,                   -- Minimum time between footstep sounds (seconds)
  MaxDistanceDelta = 10,                -- Anti-teleport protection threshold
} :: MAFSSettings
