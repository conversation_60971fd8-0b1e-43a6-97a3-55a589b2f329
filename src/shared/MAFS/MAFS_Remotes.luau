--!strict

--[[
    MAFS Remotes Module

    Manages RemoteEvent creation and access for the MAFS system

    ARCHITECTURE ROLE:
    - Centralizes RemoteEvent management
    - Ensures consistent event naming and structure
    - Provides safe access to communication layer

    USAGE:
    - Import this module to access MAFS RemoteEvents
    - Used by both client and server components

    *Dynamic Innovative Studio*
]]

local MAFSRemotes = {}

-- Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- Constants
local REMOTES_FOLDER_NAME = "MAFS"
local FOOTSTEP_EVENT_NAME = "FootstepEvent"

-- Cache for remote events
local remotesFolder = nil
local footstepEvent = nil

--[[
    Gets or creates the MAFS remotes folder

    @return (Folder) - The MAFS remotes folder
]]
function MAFSRemotes.GetRemotesFolder()
	if remotesFolder and remotesFolder.Parent then
		return remotesFolder
	end

	-- Look for existing folder
	remotesFolder = ReplicatedStorage:FindFirstChild(REMOTES_FOLDER_NAME)

	if not remotesFolder then
		-- Only create on server
		if RunService:IsServer() then
			remotesFolder = Instance.new("Folder")
			remotesFolder.Name = REMOTES_FOLDER_NAME
			remotesFolder.Parent = ReplicatedStorage
		else
			-- Client waits for server to create it
			remotesFolder = ReplicatedStorage:WaitForChild(REMOTES_FOLDER_NAME, 10)
			if not remotesFolder then
				warn("MAFS: Failed to find remotes folder after timeout")
				return nil
			end
		end
	end

	return remotesFolder
end

--[[
    Gets or creates the footstep RemoteEvent

    @return (RemoteEvent) - The footstep RemoteEvent
]]
function MAFSRemotes.GetFootstepEvent()
	if footstepEvent and footstepEvent.Parent then
		return footstepEvent
	end

	local folder = MAFSRemotes.GetRemotesFolder()
	if not folder then
		warn("MAFS: Cannot access remotes folder")
		return nil
	end

	-- Look for existing event
	footstepEvent = folder:FindFirstChild(FOOTSTEP_EVENT_NAME)

	if not footstepEvent then
		-- Only create on server
		if RunService:IsServer() then
			footstepEvent = Instance.new("RemoteEvent")
			footstepEvent.Name = FOOTSTEP_EVENT_NAME
			footstepEvent.Parent = folder
		else
			-- Client waits for server to create it
			footstepEvent = folder:WaitForChild(FOOTSTEP_EVENT_NAME, 10)
			if not footstepEvent then
				warn("MAFS: Failed to find footstep event after timeout")
				return nil
			end
		end
	end

	return footstepEvent
end

--[[
    Initializes the remotes system
    Should be called during system startup
]]
function MAFSRemotes.Initialize()
	local folder = MAFSRemotes.GetRemotesFolder()
	local event = MAFSRemotes.GetFootstepEvent()

	if folder and event then
		if RunService:IsServer() then
			print("MAFS: Server remotes initialized")
		else
			print("MAFS: Client remotes connected")
		end
		return true
	else
		warn("MAFS: Failed to initialize remotes")
		return false
	end
end

--[[
    Cleans up remote references
    Used for testing or system shutdown
]]
function MAFSRemotes.Cleanup()
	remotesFolder = nil
	footstepEvent = nil
end

return MAFSRemotes
