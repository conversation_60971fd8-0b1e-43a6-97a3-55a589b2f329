--!strict

--[[
    FootStep_Server_Initialization.server.luau

    Server script for initializing the footstep system

    ARCHITECTURE ROLE:
    - Entry point for the server footstep system
    - Creates necessary folders and references
    - Loads and initializes the ServerManager module

    PLACEMENT:
    - This script should be placed in ServerScriptService

    *Dynamic Innovative Studio*
]]

-- Services

-- Reference the server-side ServerManager module
local ServerManager = script.Parent:FindFirstChild("MAFS_Server_Module")
if not ServerManager then
	warn("MAFS: Manager module not found in expected location")
	return
end

-- Initialize the server-side footstep system
local ServerManager = require(script.Parent.MAFS_Server_Manager)
local success = ServerManager.Initialize()

if success then
	print("MAFS: Server-side footstep system initialized successfully")
else
	warn("MAFS: Failed to initialize server-side footstep system")
end
